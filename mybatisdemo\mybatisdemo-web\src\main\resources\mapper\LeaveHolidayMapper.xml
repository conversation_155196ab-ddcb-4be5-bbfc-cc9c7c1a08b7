<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunar.framework.mybatisdemo.web.mapper.LeaveHolidayMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="staff_id" property="staffId" jdbcType="INTEGER"/>
        <result column="start_date" property="startDate" jdbcType="TIMESTAMP"/>
        <result column="end_date" property="endDate" jdbcType="TIMESTAMP"/>
        <result column="day_num" property="dayNum" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, staff_id, start_date, end_date, day_num, type, area
    </sql>

    <!-- 根据ID查询请假信息 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE id = #{id}
    </select>

    <!-- 根据员工工号查询请假信息 -->
    <select id="selectByStaffId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE staff_id = #{staffId}
        ORDER BY start_date DESC
    </select>

    <!-- 查询所有请假信息 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        ORDER BY start_date DESC
    </select>

    <!-- 根据地区查询请假信息 -->
    <select id="selectByArea" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE area = #{area}
        ORDER BY start_date DESC
    </select>

    <!-- 根据请假类型查询请假信息 -->
    <select id="selectByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE type = #{type}
        ORDER BY start_date DESC
    </select>

    <!-- 根据时间范围查询请假信息 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE start_date >= #{startDate}
        AND end_date <= #{endDate}
        ORDER BY start_date DESC
    </select>

    <!-- 根据员工工号和时间范围查询请假信息 -->
    <select id="selectByStaffIdAndDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM leave_holiday
        WHERE staff_id = #{staffId}
        AND start_date >= #{startDate}
        AND end_date <= #{endDate}
        ORDER BY start_date DESC
    </select>

    <!-- 插入请假信息 -->
    <insert id="insert" parameterType="com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO leave_holiday (staff_id, start_date, end_date, day_num, type, area)
        VALUES (#{staffId}, #{startDate}, #{endDate}, #{dayNum}, #{type}, #{area})
    </insert>

    <!-- 更新请假信息 -->
    <update id="update" parameterType="com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday">
        UPDATE leave_holiday
        SET staff_id = #{staffId},
            start_date = #{startDate},
            end_date = #{endDate},
            day_num = #{dayNum},
            type = #{type},
            area = #{area}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除请假信息 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM leave_holiday WHERE id = #{id}
    </delete>

    <!-- 根据员工工号删除请假信息 -->
    <delete id="deleteByStaffId" parameterType="java.lang.Integer">
        DELETE FROM leave_holiday WHERE staff_id = #{staffId}
    </delete>

</mapper>
