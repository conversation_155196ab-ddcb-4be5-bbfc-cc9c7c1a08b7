package com.qunar.framework.mybatisdemo.web.mapper;

import com.qunar.framework.mybatisdemo.web.entity.Holiday;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工假期Mapper接口
 */
@Mapper
public interface HolidayMapper {

    /**
     * 根据ID查询假期信息
     * @param id 假期ID
     * @return 假期信息
     */
    Holiday selectById(@Param("id") Integer id);

    /**
     * 根据员工工号查询假期信息
     * @param staffId 员工工号
     * @return 假期信息
     */
    Holiday selectByStaffId(@Param("staffId") Integer staffId);

    /**
     * 查询所有假期信息
     * @return 假期信息列表
     */
    List<Holiday> selectAll();

    /**
     * 插入假期信息
     * @param holiday 假期信息
     * @return 影响行数
     */
    int insert(Holiday holiday);

    /**
     * 更新假期信息
     * @param holiday 假期信息
     * @return 影响行数
     */
    int update(Holiday holiday);

    /**
     * 根据员工工号更新病假天数
     * @param staffId 员工工号
     * @param sickNum 病假天数
     * @return 影响行数
     */
    int updateSickNumByStaffId(@Param("staffId") Integer staffId, @Param("sickNum") Integer sickNum);

    /**
     * 根据员工工号更新年假天数
     * @param staffId 员工工号
     * @param annualNum 年假天数
     * @return 影响行数
     */
    int updateAnnualNumByStaffId(@Param("staffId") Integer staffId, @Param("annualNum") Integer annualNum);

    /**
     * 根据ID删除假期信息
     * @param id 假期ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);

    /**
     * 根据员工工号删除假期信息
     * @param staffId 员工工号
     * @return 影响行数
     */
    int deleteByStaffId(@Param("staffId") Integer staffId);
}
