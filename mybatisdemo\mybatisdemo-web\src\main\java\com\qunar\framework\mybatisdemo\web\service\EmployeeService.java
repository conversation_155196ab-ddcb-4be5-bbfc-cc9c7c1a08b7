package com.qunar.framework.mybatisdemo.web.service;

import com.qunar.framework.mybatisdemo.web.entity.Employee;
import com.qunar.framework.mybatisdemo.web.mapper.EmployeeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工信息服务类
 */
@Service
public class EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    /**
     * 根据ID查询员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    public Employee getEmployeeById(Integer id) {
        return employeeMapper.selectById(id);
    }

    /**
     * 根据员工工号查询员工信息
     * @param staffId 员工工号
     * @return 员工信息
     */
    public Employee getEmployeeByStaffId(Integer staffId) {
        return employeeMapper.selectByStaffId(staffId);
    }

    /**
     * 查询所有员工信息
     * @return 员工信息列表
     */
    public List<Employee> getAllEmployees() {
        return employeeMapper.selectAll();
    }

    /**
     * 根据地区查询员工信息
     * @param area 工作地区
     * @return 员工信息列表
     */
    public List<Employee> getEmployeesByArea(String area) {
        return employeeMapper.selectByArea(area);
    }

    /**
     * 根据在职状态查询员工信息
     * @param isValid 在职状态 1:在职 2:离职
     * @return 员工信息列表
     */
    public List<Employee> getEmployeesByIsValid(Integer isValid) {
        return employeeMapper.selectByIsValid(isValid);
    }

    /**
     * 添加员工信息
     * @param employee 员工信息
     * @return 是否成功
     */
    public boolean addEmployee(Employee employee) {
        return employeeMapper.insert(employee) > 0;
    }

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 是否成功
     */
    public boolean updateEmployee(Employee employee) {
        return employeeMapper.update(employee) > 0;
    }

    /**
     * 根据ID删除员工信息
     * @param id 员工ID
     * @return 是否成功
     */
    public boolean deleteEmployeeById(Integer id) {
        return employeeMapper.deleteById(id) > 0;
    }

    /**
     * 根据员工工号删除员工信息
     * @param staffId 员工工号
     * @return 是否成功
     */
    public boolean deleteEmployeeByStaffId(Integer staffId) {
        return employeeMapper.deleteByStaffId(staffId) > 0;
    }
}
