package com.qunar.framework.mybatisdemo.web.entity;

import java.io.Serializable;

/**
 * 员工假期实体类
 */
public class Holiday implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 员工工号
     */
    private Integer staffId;

    /**
     * 病假剩余天数
     */
    private Integer sickNum;

    /**
     * 年假剩余天数
     */
    private Integer annualNum;

    // 构造方法
    public Holiday() {}

    public Holiday(Integer staffId, Integer sickNum, Integer annualNum) {
        this.staffId = staffId;
        this.sickNum = sickNum;
        this.annualNum = annualNum;
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Integer getSickNum() {
        return sickNum;
    }

    public void setSickNum(Integer sickNum) {
        this.sickNum = sickNum;
    }

    public Integer getAnnualNum() {
        return annualNum;
    }

    public void setAnnualNum(Integer annualNum) {
        this.annualNum = annualNum;
    }

    @Override
    public String toString() {
        return "Holiday{" +
                "id=" + id +
                ", staffId=" + staffId +
                ", sickNum=" + sickNum +
                ", annualNum=" + annualNum +
                '}';
    }
}
