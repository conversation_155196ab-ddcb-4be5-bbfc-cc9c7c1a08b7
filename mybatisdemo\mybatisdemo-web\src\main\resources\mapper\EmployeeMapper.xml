<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunar.framework.mybatisdemo.web.mapper.EmployeeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qunar.framework.mybatisdemo.web.entity.Employee">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="staff_id" property="staffId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="TINYINT"/>
        <result column="is_valid" property="isValid" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, staff_id, name, mobile, area, gender, is_valid
    </sql>

    <!-- 根据ID查询员工信息 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE id = #{id}
    </select>

    <!-- 根据员工工号查询员工信息 -->
    <select id="selectByStaffId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE staff_id = #{staffId}
    </select>

    <!-- 查询所有员工信息 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        ORDER BY id
    </select>

    <!-- 根据地区查询员工信息 -->
    <select id="selectByArea" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE area = #{area}
        ORDER BY id
    </select>

    <!-- 根据在职状态查询员工信息 -->
    <select id="selectByIsValid" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE is_valid = #{isValid}
        ORDER BY id
    </select>

    <!-- 插入员工信息 -->
    <insert id="insert" parameterType="com.qunar.framework.mybatisdemo.web.entity.Employee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO employee (staff_id, name, mobile, area, gender, is_valid)
        VALUES (#{staffId}, #{name}, #{mobile}, #{area}, #{gender}, #{isValid})
    </insert>

    <!-- 更新员工信息 -->
    <update id="update" parameterType="com.qunar.framework.mybatisdemo.web.entity.Employee">
        UPDATE employee
        SET staff_id = #{staffId},
            name = #{name},
            mobile = #{mobile},
            area = #{area},
            gender = #{gender},
            is_valid = #{isValid}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除员工信息 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM employee WHERE id = #{id}
    </delete>

    <!-- 根据员工工号删除员工信息 -->
    <delete id="deleteByStaffId" parameterType="java.lang.Integer">
        DELETE FROM employee WHERE staff_id = #{staffId}
    </delete>

</mapper>
