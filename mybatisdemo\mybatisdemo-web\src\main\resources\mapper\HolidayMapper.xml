<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunar.framework.mybatisdemo.web.mapper.HolidayMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qunar.framework.mybatisdemo.web.entity.Holiday">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="staff_id" property="staffId" jdbcType="INTEGER"/>
        <result column="sick_num" property="sickNum" jdbcType="INTEGER"/>
        <result column="annual_num" property="annualNum" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, staff_id, sick_num, annual_num
    </sql>

    <!-- 根据ID查询假期信息 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM holiday
        WHERE id = #{id}
    </select>

    <!-- 根据员工工号查询假期信息 -->
    <select id="selectByStaffId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM holiday
        WHERE staff_id = #{staffId}
    </select>

    <!-- 查询所有假期信息 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM holiday
        ORDER BY id
    </select>

    <!-- 插入假期信息 -->
    <insert id="insert" parameterType="com.qunar.framework.mybatisdemo.web.entity.Holiday" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO holiday (staff_id, sick_num, annual_num)
        VALUES (#{staffId}, #{sickNum}, #{annualNum})
    </insert>

    <!-- 更新假期信息 -->
    <update id="update" parameterType="com.qunar.framework.mybatisdemo.web.entity.Holiday">
        UPDATE holiday
        SET staff_id = #{staffId},
            sick_num = #{sickNum},
            annual_num = #{annualNum}
        WHERE id = #{id}
    </update>

    <!-- 根据员工工号更新病假天数 -->
    <update id="updateSickNumByStaffId">
        UPDATE holiday
        SET sick_num = #{sickNum}
        WHERE staff_id = #{staffId}
    </update>

    <!-- 根据员工工号更新年假天数 -->
    <update id="updateAnnualNumByStaffId">
        UPDATE holiday
        SET annual_num = #{annualNum}
        WHERE staff_id = #{staffId}
    </update>

    <!-- 根据ID删除假期信息 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM holiday WHERE id = #{id}
    </delete>

    <!-- 根据员工工号删除假期信息 -->
    <delete id="deleteByStaffId" parameterType="java.lang.Integer">
        DELETE FROM holiday WHERE staff_id = #{staffId}
    </delete>

</mapper>
